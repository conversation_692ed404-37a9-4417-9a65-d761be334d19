<?php

namespace App\Livewire\Settings\Socials;

use App\Models\WhatsApp\WhatsAppConnectedUser;
use Livewire\Component;
use Livewire\Attributes\On;
use App\Models\WhatsApp\WhatsAppSettings as WhatsAppSettingsModel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class WhatsappSettings extends Component
{
    public $auto_reply = false;
    public $welcome_message = '';
    public $connected_ai_model = null;
    public $whatsapp_settings_id = null;
    public $device_info = null;

    // Available AI models (placeholder - you can populate from your AI models)
    public $available_ai_models = [
        1 => 'GPT-4',
        2 => 'Claude 3',
        3 => 'Gemini Pro',
        4 => 'Custom Model'
    ];

    #[On('loadSettings')]
    public function loadSettings()
    {
        $user = Auth::user();
        if ($user) {
            // FAST: Load settings with connection info from database first
            $settings = WhatsAppSettingsModel::forUserWithConnection($user->id);

            if ($settings) {
                // Load basic settings
                $this->auto_reply = $settings->auto_reply;
                $this->welcome_message = $settings->welcome_message ?? '';
                $this->connected_ai_model = $settings->connected_ai_model;
                $this->whatsapp_settings_id = $settings->id;

                // FAST: Check if user is connected from database
                $connectedUser = $settings->connectedUser;
                if ($connectedUser) {
                    $this->device_info = [
                        'session_id' => $connectedUser->session_id,
                        'phone_number' => $connectedUser->phone_number,
                        'profile_name' => $connectedUser->profile_name,
                        'profile_picture_url' => $connectedUser->profile_picture_url,
                        'device_model' => $connectedUser->device_model,
                        'platform' => $connectedUser->platform,
                    ];
                } else {
                    $this->device_info = null;
                }
            } else {
                $settings = WhatsAppSettingsModel::updateOrCreateForUser($user->id, [
                    'auto_reply' => false,
                    'welcome_message' => '',
                    'connected_ai_model' => null,
                ]);

                $this->whatsapp_settings_id = $settings->id;

                // Also update the component properties
                $this->auto_reply = $settings->auto_reply;
                $this->welcome_message = $settings->welcome_message ?? '';
                $this->connected_ai_model = $settings->connected_ai_model;
            }
        }
    }

    /**
     * Update device info (called when user gets authenticated)
     */
    #[On('whatsapp-authenticated')]
    public function updateDeviceInfo()
    {
        try {
            $userId = Auth::id();

            if (!$this->device_info) {
                Log::warning('No device info received in updateDeviceInfo method');
                return;
            }

            // Save the connected user information
            $connectedUser = WhatsAppConnectedUser::updateOrCreateFromClientInfo(
                $this->whatsapp_settings_id,
                $this->device_info
            );

            Log::info('Connected WhatsApp user saved successfully', [
                'user_id' => $userId,
                'connected_user_id' => $connectedUser->id
            ]);
        } catch (\Exception $e) {
            Log::error('Error saving connected WhatsApp user: ' . $e->getMessage(), [
                'user_id' => $userId ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    public function saveSettings()
    {
        $this->validate([
            'welcome_message' => 'nullable|string|max:1000',
            'connected_ai_model' => 'nullable|integer',
        ]);

        $user = Auth::user();
        if ($user) {
            WhatsAppSettingsModel::updateOrCreateForUser($user->id, [
                'auto_reply' => $this->auto_reply,
                'welcome_message' => $this->welcome_message,
                'connected_ai_model' => $this->connected_ai_model,
            ]);

            $this->dispatch('settings-saved', 'WhatsApp settings saved successfully!');
        }
    }

    public function mount()
    {
        $this->dispatch('loadSettings');
    }


    public function render()
    {
        return view('livewire.settings.socials.whatsapp-settings');
    }
}
