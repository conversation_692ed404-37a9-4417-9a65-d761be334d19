/**
 * WhatsApp Authentication Handler
 * Handles all WhatsApp authentication states and API communication
 */

let isConnecting = false;
let currentUserId = null;
let reverbChannel = null;
let authCallbacks = {};

// Get user ID from <PERSON><PERSON> (you can customize this based on your auth system)
function getUserId() {
    // Try to get user ID from meta tag
    const metaTag = document.querySelector('meta[name="user-id"]');
    if (metaTag) {
        return metaTag.getAttribute('content');
    }

    // Fallback: try to get from window.Laravel if available
    if (window.Laravel && window.Laravel.user && window.Laravel.user.id) {
        return window.Laravel.user.id.toString();
    }

    // Fallback: generate a session-based ID
    let sessionId = sessionStorage.getItem('whatsapp_session_id');
    if (!sessionId) {
        sessionId = 'session_' + Math.random().toString(36).substring(2, 11);
        sessionStorage.setItem('whatsapp_session_id', sessionId);
    }
    return sessionId;
}

// Get auth token (customize based on your auth system)
function getAuthToken() {
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    return metaTag ? metaTag.getAttribute('content') : null;
}

/**
 * Connect to WhatsApp via API calls
 */
export function connectWhatsApp({ onReady, onAuthenticating, onAuthenticated, onConnecting, onServerUnavailable }) {
    // GUARD: Prevent multiple simultaneous connections
    if (isConnecting) {
        console.log('⚠️ WhatsApp connection already in progress, skipping...');
        return;
    }

    isConnecting = true;
    currentUserId = getUserId();

    console.log('🚀 Starting WhatsApp connection for user:', currentUserId);

    // Start with connecting status as default
    if (onConnecting) {
        onConnecting('Connecting to WhatsApp, please wait...');
    }

    // First, check if user session already exists
    checkExistingSession({ onReady, onAuthenticating, onAuthenticated, onConnecting, onServerUnavailable });
}

/**
 * Check if user session already exists on WhatsApp server or create new one
 */
function checkExistingSession({ onReady, onAuthenticating, onAuthenticated, onConnecting, onServerUnavailable }) {
    // Use the new check-connection endpoint that handles both checking and creating
    makeApiCall('/check-connection', { userId: currentUserId })
        .then(response => {
            console.log('📊 Connection check response:', response);

            const { status, isExisting } = response;

            if (isExisting) {
                console.log('♻️ Using existing WhatsApp session');
            } else {
                console.log('🆕 Created new WhatsApp session');
            }

            // If session exists and is already authenticated, use it
            if (status && status.status === 'connected' && status.clientInfo) {
                console.log('♻️ Using existing connected session with client info:', status.clientInfo);
                isConnecting = false;
                // Immediately trigger authenticated callback with existing client info
                if (onAuthenticated) {
                    onAuthenticated(status.clientInfo);
                }
                startReverbListener({ onReady, onAuthenticating, onAuthenticated, onConnecting });
                return;
            }

            // If session exists but not authenticated, start listening and let it continue
            if (status && (status.status === 'ready' || status.status === 'connecting' || status.status === 'initializing')) {
                console.log('♻️ Resuming existing session with status:', status.status);
                // Trigger appropriate callback based on current status
                if (status.status === 'ready' && status.qrCode && onReady) {
                    onReady(status.qrCode);
                } else if (status.status === 'connecting' && onConnecting) {
                    onConnecting('Connecting to WhatsApp, please wait...');
                }
                startReverbListener({ onReady, onAuthenticating, onAuthenticated, onConnecting });
                return;
            }

            // For any other status, start listening for updates
            startReverbListener({ onReady, onAuthenticating, onAuthenticated, onConnecting });

        }).catch(error => {
            console.error('❌ WhatsApp server unavailable:', error.message);
            isConnecting = false;

            // Handle server unavailability
            handleServerUnavailable(error, { onReady, onAuthenticating, onAuthenticated, onConnecting, onServerUnavailable });
        });
}

/**
 * Handle server unavailability
 */
function handleServerUnavailable(error, { onConnecting, onServerUnavailable }) {
    console.error('🚫 WhatsApp server is unavailable:', error.message);

    // Determine server status message based on error type
    let statusMessage = 'WhatsApp server is currently unavailable';

    if (error.message.includes('fetch')) {
        statusMessage = 'Cannot connect to WhatsApp server - server may be down or unreachable';
    } else if (error.message.includes('timeout')) {
        statusMessage = 'WhatsApp server connection timeout - server may be overloaded';
    } else if (error.message.includes('ECONNREFUSED')) {
        statusMessage = 'WhatsApp server connection refused - server is not running';
    } else if (error.message.includes('ENOTFOUND')) {
        statusMessage = 'WhatsApp server not found - check server configuration';
    } else if (error.message.includes('HTTP 500')) {
        statusMessage = 'WhatsApp server internal error - server is experiencing issues';
    } else if (error.message.includes('HTTP 503')) {
        statusMessage = 'WhatsApp server temporarily unavailable - server is under maintenance';
    }

    // Call the server unavailable callback if provided
    if (onServerUnavailable) {
        onServerUnavailable(statusMessage);
    } else if (onConnecting) {
        // Fallback to connecting callback if server unavailable callback not provided
        onConnecting('Unable to connect to WhatsApp server');
    }
}


/**
 * Logout WhatsApp session
 */
export function logoutWhatsApp() {
    console.log('🚪 Logging out WhatsApp session for user:', currentUserId);

    return makeApiCall('/logout', {
        userId: currentUserId
    })
        .then(response => {
            console.log('✅ WhatsApp session logged out:', response);
            return response;
        })
        .catch(error => {
            console.error('❌ Failed to logout WhatsApp:', error);
            throw error;
        });
}

/**
 * Get WhatsApp connection status (uses check-connection endpoint)
 */
export function getWhatsAppStatus() {
    return makeApiCall('/check-connection', { userId: currentUserId || getUserId() })
        .then(response => {
            console.log('📊 WhatsApp status:', response);
            return response;
        })
        .catch(error => {
            console.error('❌ Failed to get WhatsApp status:', error);
            throw error;
        });
}

/**
 * Disconnect WhatsApp session
 */
export function disconnectWhatsApp() {
    console.log('🔌 Disconnecting WhatsApp session for user:', currentUserId);

    return makeApiCall('/logout', {
        userId: currentUserId || getUserId()
    })
        .then(response => {
            console.log('✅ WhatsApp session disconnected:', response);

            // Clean up local state
            isConnecting = false;

            // Clean up Reverb channel
            if (reverbChannel) {
                try {
                    reverbChannel.unsubscribe();
                    console.log('🔗 Unsubscribed from Reverb channel');
                } catch (error) {
                    console.warn('⚠️ Error unsubscribing from Reverb channel:', error);
                }
                reverbChannel = null;
            }

            // Clear callbacks
            authCallbacks = {};

            return response;
        })
        .catch(error => {
            console.error('❌ Failed to disconnect WhatsApp:', error);
            throw error;
        });
}


/**
 * Start listening for auth status updates via Reverb
 */
function startReverbListener(callbacks) {
    authCallbacks = callbacks;

    if (!currentUserId) {
        console.error('❌ Cannot start Reverb listener: No user ID');
        return;
    }

    // Subscribe to private channel for this user
    const channelName = `whatsapp.auth.${currentUserId}`;

    try {
        // Using Laravel Echo (assuming it's available globally)
        if (window.Echo && window.Echo.connector) {
            console.log('🔗 Echo is available, subscribing to channel:', channelName);

            reverbChannel = window.Echo.private(channelName);

            // Listen for auth status events
            reverbChannel.listen('.auth.status', (event) => {
                console.log('📡 Received auth status via Reverb:', event);
                handleAuthStatus(event);
            });

            // Listen for subscription success
            reverbChannel.subscribed(() => {
                console.log('✅ Successfully subscribed to channel:', channelName);
            });

            // Add error handling for channel
            reverbChannel.error((error) => {
                console.error('❌ Reverb channel error:', error);
            });

            console.log(`✅ Subscribed to Reverb channel: ${channelName}`);
        } else {
            console.error('❌ Laravel Echo not available - check if Echo is loaded');
            console.log('Available globals:', Object.keys(window).filter(key => key.includes('Echo') || key.includes('echo')));
        }
    } catch (error) {
        console.error('❌ Error setting up Reverb listener:', error);
    }
}


/**
 * Handle auth status updates
 */
function handleAuthStatus(statusData) {
    const { status, body } = statusData;

    switch (status) {
        case 'ready':
            if (authCallbacks.onReady && body) {
                authCallbacks.onReady(body); // body contains QR code
            }
            break;

        case 'authenticating':
            console.log('🔄 WhatsApp authenticating...');
            if (authCallbacks.onAuthenticating) {
                authCallbacks.onAuthenticating(body || 'Authenticating, please wait...'); // body contains progress message
            }
            break;

        case 'authenticated':
            console.log('✅ WhatsApp authentication in progress...');
            // Note: Device info usually comes in the subsequent 'ready' status
            if (body && typeof body === 'object' && body.pushname) {
                console.log('📱 Device info received:', body);
                isConnecting = false;
                if (authCallbacks.onAuthenticated) {
                    authCallbacks.onAuthenticated(body);
                }
            }
            break;
        default:
            console.log('🔄 WhatsApp connecting...');
            if (authCallbacks.onConnecting) {
                authCallbacks.onConnecting(body || 'Connecting to WhatsApp, please wait...');
            }
            break;
    }
}

/**
 * Make API call to WhatsApp server
 */
async function makeApiCall(endpoint, data = null, method = 'POST') {
    const url = `http://localhost:3001${endpoint}`;

    const options = {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    };

    if (data && method !== 'GET') {
        options.body = JSON.stringify(data);
    }

    try {
        const response = await fetch(url, options);

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
            throw new Error(errorData.error || `HTTP ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        // Handle network errors (server down, connection refused, etc.)
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            throw new Error('ECONNREFUSED - WhatsApp server is not running or unreachable');
        } else if (error.message.includes('timeout')) {
            throw new Error('timeout - WhatsApp server connection timeout');
        } else {
            // Re-throw the original error if it's not a network error
            throw error;
        }
    }
}
/**
 * Get current user ID
 */
export function getCurrentUserId() {
    return currentUserId || getUserId();
}
