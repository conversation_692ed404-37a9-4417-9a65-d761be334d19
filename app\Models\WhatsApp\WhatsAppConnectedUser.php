<?php

namespace App\Models\WhatsApp;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use App\Traits\CreatesDatabaseFile;
use Illuminate\Support\Facades\Log;

class WhatsAppConnectedUser extends Model
{
    use HasFactory, CreatesDatabaseFile;

    protected $connection = 'whatsapp';
    protected $table = 'connected_users';

    protected $fillable = [
        'wa_settings_id',
        'session_id',
        'phone_number',
        'profile_name',
        'profile_picture_url',
        'device_model',
        'platform',
    ];

    /**
     * Get the WhatsApp settings that owns this connected user
     */
    public function waSettings(): BelongsTo
    {
        return $this->belongsTo(WhatsAppSettings::class, 'wa_settings_id');
    }

    /**
     * Get all WhatsApp profiles for this connected user
     */
    public function profiles(): Has<PERSON>any
    {
        return $this->hasMany(WhatsAppProfile::class, 'connected_user_id');
    }

    /**
     * Get the connected user for a specific user ID (through settings)
     */
    public static function forUser($userId)
    {
        return static::whereHas('waSettings', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->first();
    }

    /**
     * Create or update connected user with client info
     */
    public static function updateOrCreateFromClientInfo($waSettingsId, $sessionId, $clientInfo = null)
    {

        // Extract data from client info
        $data = [
            'session_id' => $sessionId,
            'wa_settings_id' => $waSettingsId,
        ];

        // Extract specific fields from client_info if available
        if ($clientInfo && is_array($clientInfo)) {
            if (isset($clientInfo['wid']['user'])) {
                $data['phone_number'] = $clientInfo['wid']['user'];
            }

            if (isset($clientInfo['pushname'])) {
                $data['profile_name'] = $clientInfo['pushname'];
            }

            if (isset($clientInfo['profilePicUrl'])) {
                $data['profile_picture_url'] = $clientInfo['profilePicUrl'];
            }

            if (isset($clientInfo['phone']['device_model'])) {
                $data['device_model'] = $clientInfo['phone']['device_model'];
            }

            if (isset($clientInfo['platform'])) {
                $data['platform'] = $clientInfo['platform'];
            }
        }
        Log::info('Connected user data being saved:', $data);

        return static::updateOrCreate(
            ['wa_settings_id' => $waSettingsId],
            $data
        );
    }

    /**
     * Create a basic connection record without client info (for initial connection)
     */
    public static function createBasicConnection($userId, $sessionId)
    {
        return static::updateOrCreate(
            ['user_id' => $userId],
            [
                'session_id' => $sessionId,
            ]
        );
    }

    /**
     * Disconnect user (remove from connected users)
     */
    public static function disconnectUser($userId = null)
    {
        if (!$userId) {
            try {
                $userId = Auth::id();
            } catch (\Exception) {
                // If auth fails, we can't determine user ID
                return false;
            }
        }

        if ($userId) {
            return static::where('user_id', $userId)->delete();
        }
        return false;
    }
}
